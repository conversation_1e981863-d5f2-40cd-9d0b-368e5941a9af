import { Component, h, Prop, State, Element } from '@stencil/core';
import { getResponsesApi, ResponseFilters } from './helpers';

@Component({
  tag: 'p-survey-responses',
  styleUrl: 'p-survey-responses.css',
  shadow: true,
})
export class PSurveyResponses {
  @Element() el: HTMLElement;
  @Prop() surveyId: string;
  @Prop() survey: any;

  @State() selectedTimeFilter: string = 'all-time';

  @State() responses: any[] = [];
  @State() analytics: any = {};
  @State() loading: boolean = false;
  @State() error: string = null;
  @State() selectedResponse: any = null;
  @State() showModal: boolean = false;

  private timeFilterOptions = [
    { label: 'All time', value: 'all-time' },
    { label: 'Last 7 days', value: '7-days' },
    { label: 'Last 30 days', value: '30-days' },
    { label: 'Last 90 days', value: '90-days' },
  ];

  async componentDidLoad() {
    await this.fetchResponses();
  }

  private async fetchResponses() {
    if (!this.surveyId) return;

    this.loading = true;
    this.error = null;

    try {
      const filters: ResponseFilters = {
        timeFilter: this.selectedTimeFilter,
      };

      const result = await getResponsesApi(this.surveyId, filters);

      if (result.success) {
        this.responses = result.payload.responses || [];
        this.analytics = result.payload || {};
        await this.renderGraph();
      } else {
        this.error = result.message;
      }
    } catch (error) {
      this.error = 'Failed to fetch responses';
      console.error('Error fetching responses:', error);
    } finally {
      this.loading = false;
    }
  }

  private async renderGraph() {
    try {
      // Dynamically import Plotly to avoid bundle size issues
      const Plotly = await import('plotly.js-dist-min');

      const graphElement = this.el.shadowRoot.querySelector('#responses-graph');
      if (!graphElement || !this.analytics.responsesByDay) return;

      // Prepare data for the line chart
      const dates = Object.keys(this.analytics.responsesByDay).sort();
      const counts = dates.map(date => this.analytics.responsesByDay[date]);

      const data = [
        {
          x: dates,
          y: counts,
          type: 'scatter',
          mode: 'lines+markers',
          line: {
            color: '#3A8DFF',
            width: 2,
          },
          marker: {
            color: '#3A8DFF',
            size: 6,
          },
          name: 'Responses',
        },
      ];

      const layout = {
        title: {
          text: 'Responses Over Time',
          font: { size: 16, color: '#333' },
        },
        xaxis: {
          title: 'Date',
          showgrid: true,
          gridcolor: '#f0f0f0',
        },
        yaxis: {
          title: 'Number of Responses',
          showgrid: true,
          gridcolor: '#f0f0f0',
        },
        plot_bgcolor: 'white',
        paper_bgcolor: 'white',
        margin: { t: 50, r: 20, b: 50, l: 50 },
        height: 300,
      };

      const config = {
        displayModeBar: false,
        responsive: true,
      };

      Plotly.newPlot(graphElement, data, layout, config);
    } catch (error) {
      console.error('Error rendering graph:', error);
    }
  }

  private handleTimeFilterChange = async (event: any) => {
    this.selectedTimeFilter = event.detail.value;
    await this.fetchResponses();
  };

  private getRespondentName(response: any): string {
    return response.respondent_details?.name || response.respondent_details?.firstName || '-';
  }

  private getRespondentEmail(response: any): string {
    return response.respondent_details?.email || '-';
  }

  private formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  private formatObjectForDisplay(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    const formatKey = (key: string): string => {
      // Convert camelCase to space-separated words
      const spaced = key.replace(/([A-Z])/g, ' $1');
      // Convert to uppercase
      return spaced.toUpperCase().trim();
    };

    const formatValue = (value: any): any => {
      // Handle empty, null, undefined, or empty string values
      if (
        value === null ||
        value === undefined ||
        value === '' ||
        (typeof value === 'string' && value.trim() === '')
      ) {
        return '-';
      }

      // Handle nested objects (like userAgent) - return as array for list rendering
      if (typeof value === 'object' && value !== null) {
        return Object.entries(value).map(([nestedKey, nestedValue]) => ({
          key: nestedKey,
          value: nestedValue,
        }));
      }

      return value;
    };

    return Object.entries(obj).map(([key, value]) => ({
      label: formatKey(key),
      value: formatValue(value),
    }));
  }

  private renderValue(value: any) {
    // If value is an array of key-value pairs (nested object), render as list
    if (
      Array.isArray(value) &&
      value.length > 0 &&
      typeof value[0] === 'object' &&
      'key' in value[0]
    ) {
      return (
        <ul style={{ margin: '0', paddingLeft: '1.5em' }}>
          {value.map((item: any) => (
            <li key={item.key}>
              <e-text>
                {item.key}: {item.value}
              </e-text>
            </li>
          ))}
        </ul>
      );
    }

    // For simple values, render as text
    return <e-text>{value}</e-text>;
  }

  private getCompletionRate(): number {
    if (!this.responses.length) return 0;

    const completedResponses = this.responses.filter(response => !response.is_discarded).length;
    const totalResponses = this.responses.length;

    return Math.round((completedResponses / totalResponses) * 100);
  }

  private openResponseModal = (response: any) => {
    this.selectedResponse = response;
    this.showModal = true;
  };

  private closeModal = () => {
    this.showModal = false;
    this.selectedResponse = null;
  };

  private deleteResponse = async (responseId: string) => {
    if (confirm('Are you sure you want to delete this response? This action cannot be undone.')) {
      try {
        // TODO: Implement delete API call
        console.log('Deleting response:', responseId);
        // After successful deletion, reload the responses
        await this.fetchResponses();
      } catch (error) {
        console.error('Error deleting response:', error);
        alert('Failed to delete response. Please try again.');
      }
    }
  };

  render() {
    return (
      <div>
        {/* Header with title and filters */}
        <l-row justifyContent="space-between" align="center">
          <e-text variant="heading">Responses</e-text>

          <l-row align="center">
            <e-select
              value={this.selectedTimeFilter}
              options={JSON.stringify(this.timeFilterOptions)}
              name="timeFilter"
              onSelectChangeEvent={this.handleTimeFilterChange}
              style={{ marginRight: '1em' }}
            />
          </l-row>
        </l-row>

        <l-spacer value={1}></l-spacer>

        {/* Error State */}
        {this.error && (
          <c-card>
            <div style={{ textAlign: 'center', padding: '2em' }}>
              <e-text variant="footnote" style={{ color: 'var(--color__red--600)' }}>
                ⚠️ {this.error}
              </e-text>
              <l-spacer value={1}></l-spacer>
              <e-button variant="outline" onClick={() => this.fetchResponses()}>
                Try Again
              </e-button>
            </div>
          </c-card>
        )}

        {/* Loading State */}
        {this.loading && (
          <c-card>
            <div style={{ textAlign: 'center', padding: '2em' }}>
              <e-text variant="footnote">Loading responses...</e-text>
            </div>
          </c-card>
        )}

        {/* Overview Cards */}
        {!this.loading && !this.error && (
          <l-row>
            <c-card style={{ flex: '1', marginRight: '1em' }}>
              <div style={{ textAlign: 'center' }}>
                <e-text variant="footnote">TOTAL RESPONSES</e-text>
                <l-spacer value={0.25}></l-spacer>
                <e-text variant="display">{this.analytics.totalResponses || 0}</e-text>
              </div>
            </c-card>
            <c-card style={{ flex: '1', marginRight: '1em' }}>
              <div style={{ textAlign: 'center' }}>
                <e-text variant="footnote">COMPLETION RATE</e-text>
                <l-spacer value={0.25}></l-spacer>
                <e-text variant="display">{this.getCompletionRate()}%</e-text>
              </div>
            </c-card>
            <c-card style={{ flex: '1' }}>
              <div style={{ textAlign: 'center' }}>
                <e-text variant="footnote">AVERAGE TIME</e-text>
                <l-spacer value={0.25}></l-spacer>
                <e-text variant="display">{this.analytics.avgCompletionTime || 0}m</e-text>
              </div>
            </c-card>
          </l-row>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Graph */}
        {!this.loading && !this.error && this.analytics.responsesByDay && (
          <c-card>
            <div id="responses-graph" style={{ width: '100%', height: '300px' }}></div>
          </c-card>
        )}

        <l-spacer value={2}></l-spacer>

        {/* Responses Content */}
        <c-card>
          {this.responses.length === 0 && !this.loading ? (
            <div class="responses-table-placeholder">
              <e-text variant="footnote">No responses yet</e-text>
              <l-spacer value={0.5}></l-spacer>
              <e-text>
                Individual response data will appear here once participants start submitting
                responses.
              </e-text>
              <l-spacer value={1}></l-spacer>
              <e-text variant="footnote">Features will include:</e-text>
              <ul>
                <li>
                  <e-text variant="footnote">Response timestamps</e-text>
                </li>
                <li>
                  <e-text variant="footnote">Participant details (if collected)</e-text>
                </li>
                <li>
                  <e-text variant="footnote">Individual answer data</e-text>
                </li>
                <li>
                  <e-text variant="footnote">Response completion status</e-text>
                </li>
              </ul>
            </div>
          ) : (
            <div>
              {/* Response Table */}
              <div class="response-table">
                <div class="table-header">
                  <div class="table-cell">Name</div>
                  <div class="table-cell">Email</div>
                  <div class="table-cell">Submitted On</div>
                </div>

                {this.responses.map(response => (
                  <div
                    key={response.id}
                    class="table-row"
                    onClick={() => this.openResponseModal(response)}
                  >
                    <div class="table-cell">{this.getRespondentName(response)}</div>
                    <div class="table-cell">{this.getRespondentEmail(response)}</div>
                    <div class="table-cell">{this.formatDate(response.created_at)}</div>
                    <div class="table-cell-action">
                      <button
                        class="delete-button"
                        onClick={e => {
                          e.stopPropagation();
                          this.deleteResponse(response.id);
                        }}
                        title="Delete response"
                      >
                        <e-image
                          src="../../../assets/icon/red/trash-red.svg"
                          width="1.2em"
                        ></e-image>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </c-card>

        {/* Response Detail Modal */}
        <p-modal
          is-open={this.showModal}
          modal-title="Response Details"
          onModalCloseEvent={this.closeModal}
        >
          {this.showModal && this.selectedResponse && (
            <div>
              <e-text variant="footnote">
                Submitted On: {this.formatDate(this.selectedResponse.created_at)}
              </e-text>
              <l-spacer value={1}></l-spacer>

              {this.selectedResponse.respondent_details && (
                <div>
                  <l-spacer value={1.5}></l-spacer>
                  <c-card>
                    <div class="response-section">
                      <e-text>
                        <strong>Respondent Details</strong>
                      </e-text>
                      <l-spacer value={1}></l-spacer>
                      {this.formatObjectForDisplay(this.selectedResponse.respondent_details).map(
                        (item: any) => (
                          <div style={{ marginBottom: '1em' }}>
                            <e-text variant="footnote">{item.label}</e-text>
                            {this.renderValue(item.value)}
                          </div>
                        ),
                      )}
                    </div>
                  </c-card>
                </div>
              )}

              <l-spacer value={1.5}></l-spacer>
              <c-card>
                <div class="response-section">
                  <e-text>
                    <strong>Response Data</strong>
                  </e-text>
                  <l-spacer value={1}></l-spacer>
                  {this.formatObjectForDisplay(this.selectedResponse.response_data).map(
                    (item: any) => (
                      <div style={{ marginBottom: '1em' }}>
                        <e-text variant="footnote">{item.label}</e-text>
                        {this.renderValue(item.value)}
                      </div>
                    ),
                  )}
                </div>
              </c-card>

              {this.selectedResponse.meta && (
                <div>
                  <l-spacer value={1.5}></l-spacer>
                  <c-card>
                    <div class="response-section">
                      <e-text>
                        <strong>Metadata</strong>
                      </e-text>
                      <l-spacer value={1}></l-spacer>
                      {this.formatObjectForDisplay(this.selectedResponse.meta).map((item: any) => (
                        <div style={{ marginBottom: '1em' }}>
                          <e-text variant="footnote">{item.label}</e-text>
                          {this.renderValue(item.value)}
                        </div>
                      ))}
                    </div>
                  </c-card>
                </div>
              )}
            </div>
          )}
        </p-modal>
      </div>
    );
  }
}
